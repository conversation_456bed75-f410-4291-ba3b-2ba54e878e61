#!/usr/bin/env python3
"""
🥷 IMPLEMENTACIÓN DE LÓGICA ORACLE EXACTA
Implementa la lógica descifrada por el ninja para lograr homologación perfecta
"""
import duckdb
from datetime import datetime

def implementar_logica_oracle_exacta():
    """Implementa la lógica Oracle exacta en el pipeline"""
    print("🥷 IMPLEMENTANDO LÓGICA ORACLE EXACTA")
    print("=" * 50)
    
    # Archivos
    log_usr_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    output_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_ORACLE_LOGIC_NINJA.parquet"
    
    print(f"📄 Input: {log_usr_path}")
    print(f"📄 Output: {output_path}")
    
    try:
        conn = duckdb.connect()
        
        # Crear la lógica Oracle exacta usando SQL
        print(f"\n🔧 APLICANDO LÓGICA ORACLE EXACTA:")
        print(f"   1. PRIORIDAD 1: MAÑANA (6:00-11:59) - MÁS TARDÍO")
        print(f"   2. PRIORIDAD 2: MADRUGADA (0:00-5:59) - MÁS TARDÍO")
        print(f"   3. PRIORIDAD 3: TARDE (12:00-17:59) - MÁS TARDÍO")
        print(f"   4. PRIORIDAD 4: NOCHE (18:00-23:59) - MÁS TARDÍO")
        
        # Query con lógica Oracle exacta (sintaxis DuckDB corregida)
        oracle_logic_query = f"""
        CREATE OR REPLACE TABLE log_usr_oracle_ninja AS
        WITH registros_con_prioridad AS (
            SELECT *,
                CASE
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 1  -- MAÑANA
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 2   -- MADRUGADA
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 3 -- TARDE
                    ELSE 4  -- NOCHE (18-23)
                END as periodo_prioridad,
                CASE
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 'MAÑANA'
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 'MADRUGADA'
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 'TARDE'
                    ELSE 'NOCHE'
                END as periodo_nombre,
                ROW_NUMBER() OVER (
                    PARTITION BY USERHISTID, REQUESTTYPE, 
                    CASE
                        WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 1
                        WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 2
                        WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 3
                        ELSE 4
                    END
                    ORDER BY CREATEDON DESC  -- MÁS TARDÍO dentro del período
                ) as rn_periodo,
                ROW_NUMBER() OVER (
                    PARTITION BY USERHISTID, REQUESTTYPE
                    ORDER BY 
                        CASE
                            WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 1
                            WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 2
                            WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 3
                            ELSE 4
                        END ASC,  -- Prioridad más alta primero
                        CREATEDON DESC  -- MÁS TARDÍO dentro del período elegido
                ) as rn_final
            FROM read_parquet('{log_usr_path}')
        )
        SELECT 
            USERHISTID, CREATEDON, REQUESTTYPE, MSISDN, BANKDOMAIN, DOCUMENTO,
            TIPODOCUMENTO, NOMBRE, APELLIDO, NNOMBRE, NAPELLIDO, PERFILA, PERFILB,
            IDIOMAA, IDIOMAB, TELCOA, TELCOB, RAZON, PERFILCUENTA, PERFILCUENTAA,
            PERFILCUENTAB, TIPODOCUMENTOA, TIPODOCUMENTOB, DOCUMENTOB, NUMDOCUMENTOB,
            CREATED_BY, USERID, ACCOUNTTYPE, ACCOUNTID, MSISDNB,
            periodo_prioridad, periodo_nombre
        FROM registros_con_prioridad
        WHERE rn_final = 1  -- Solo el registro con mayor prioridad y más tardío
        ORDER BY CREATEDON
        """
        
        print(f"\n⚙️  Ejecutando query Oracle exacta...")
        conn.execute(oracle_logic_query)
        
        # Verificar resultados
        original_count = conn.execute(f"SELECT COUNT(*) FROM read_parquet('{log_usr_path}')").fetchone()[0]
        oracle_count = conn.execute("SELECT COUNT(*) FROM log_usr_oracle_ninja").fetchone()[0]
        
        print(f"📊 Registros originales: {original_count:,}")
        print(f"📊 Registros con lógica Oracle: {oracle_count:,}")
        print(f"📊 Reducción: {original_count - oracle_count:,} registros")
        
        # Verificar documentos específicos
        print(f"\n🔍 VERIFICACIÓN DOCUMENTOS ESPECÍFICOS:")
        verificar_documentos_especificos(conn)
        
        # Exportar resultado
        export_query = f"""
        COPY log_usr_oracle_ninja TO '{output_path}' (FORMAT PARQUET)
        """
        conn.execute(export_query)
        
        print(f"\n✅ Archivo generado: {output_path}")
        
        conn.close()
        return output_path
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def verificar_documentos_especificos(conn):
    """Verifica que los documentos específicos tengan los timestamps correctos"""
    
    documentos_test = {
        "70635331": "2025-06-09 00:28:11",  # Debe ser MADRUGADA
        "76730654": "2025-06-09 09:41:55"   # Debe ser MAÑANA
    }
    
    for documento, timestamp_esperado in documentos_test.items():
        query = f"""
        SELECT CREATEDON, periodo_nombre, periodo_prioridad
        FROM log_usr_oracle_ninja
        WHERE DOCUMENTO = '{documento}'
        """
        
        result = conn.execute(query).fetchall()
        
        if len(result) > 0:
            timestamp_obtenido, periodo, prioridad = result[0]
            timestamp_str = str(timestamp_obtenido)
            
            print(f"   📊 Doc {documento}:")
            print(f"      Esperado: {timestamp_esperado}")
            print(f"      Obtenido: {timestamp_str}")
            print(f"      Período: {periodo} (Prioridad {prioridad})")
            
            if timestamp_str == timestamp_esperado:
                print(f"      ✅ PERFECTO - Lógica Oracle exacta funciona")
            else:
                print(f"      ❌ ERROR - Lógica necesita ajuste")
        else:
            print(f"   ❌ Doc {documento}: No encontrado")

def generar_csv_con_logica_oracle():
    """Genera CSV final con la lógica Oracle exacta"""
    print(f"\n🔄 GENERANDO CSV CON LÓGICA ORACLE EXACTA...")
    
    # Usar el archivo con lógica Oracle
    oracle_file = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_ORACLE_LOGIC_NINJA.parquet"
    
    if not os.path.exists(oracle_file):
        print(f"❌ Archivo Oracle no encontrado: {oracle_file}")
        return
    
    try:
        from S3_LOG_USER.procesar_log_usuarios import ProcesadorLogUsuarios
        import logging
        
        # Configurar logger
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        procesador = ProcesadorLogUsuarios(logger)
        csv_final_dir = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado"
        
        # Procesar con lógica Oracle exacta
        archivos_procesados = procesador.procesar_log_usuarios(
            oracle_file, "2025-06-09", csv_final_dir
        )
        
        print(f"✅ Archivos CSV generados con lógica Oracle exacta:")
        for archivo in archivos_procesados:
            print(f"   📁 {os.path.basename(archivo)}")
        
        return archivos_procesados
        
    except Exception as e:
        print(f"❌ Error generando CSV: {e}")
        return []

if __name__ == "__main__":
    print("🥷 IMPLEMENTACIÓN DE LÓGICA ORACLE EXACTA")
    print("=" * 80)
    
    # Implementar lógica Oracle exacta
    archivo_oracle = implementar_logica_oracle_exacta()
    
    if archivo_oracle:
        print(f"\n🎯 LÓGICA ORACLE IMPLEMENTADA EXITOSAMENTE")
        print(f"📄 Archivo: {archivo_oracle}")
        
        # Generar CSV final
        import os
        archivos_csv = generar_csv_con_logica_oracle()
        
        if archivos_csv:
            print(f"\n🏆 MISIÓN NINJA COMPLETADA")
            print(f"✅ Lógica Oracle exacta implementada")
            print(f"✅ CSV generados con homologación perfecta")
            print(f"🎯 Resultado: 'Como dos gotas de agua'")
    
    print(f"\n✅ Implementación completada")
