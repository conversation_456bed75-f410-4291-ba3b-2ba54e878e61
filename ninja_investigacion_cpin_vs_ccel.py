#!/usr/bin/env python3
"""
🥷 NINJA INVESTIGACIÓN CPIN vs CCEL
Misión: Investigar por qué algunos documentos tienen CPIN en modernizado y CCEL en Oracle
Objetivo: Lograr 100% de homologación perfecta
"""
import pandas as pd
import duckdb
from datetime import datetime

def ninja_investigacion_cpin_ccel():
    """Investigación ninja de diferencias CPIN vs CCEL"""
    print("🥷 NINJA INVESTIGACIÓN CPIN vs CCEL")
    print("=" * 50)
    
    # Archivos
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609212231.csv"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    log_usr_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_ORACLE_CORREGIDA.parquet"
    
    print(f"🎯 MISIÓN: Encontrar por qué Oracle cambia CPIN a CCEL")
    print(f"📄 Modernizado: {archivo_modernizado.split('/')[-1]}")
    print(f"📄 Oracle: {archivo_oracle.split('/')[-1]}")
    print(f"📄 Datos originales: {log_usr_original.split('/')[-1]}")
    
    # 1. Identificar documentos con diferencias CPIN vs CCEL
    print(f"\n1️⃣ IDENTIFICANDO DOCUMENTOS CON DIFERENCIAS CPIN vs CCEL:")
    documentos_diferentes = identificar_documentos_cpin_ccel(archivo_modernizado, archivo_oracle)
    
    # 2. Analizar datos originales de estos documentos
    print(f"\n2️⃣ ANÁLISIS DE DATOS ORIGINALES:")
    analizar_datos_originales_cpin_ccel(log_usr_original, documentos_diferentes)
    
    # 3. Buscar patrón en las diferencias
    print(f"\n3️⃣ BÚSQUEDA DE PATRÓN NINJA:")
    patron_encontrado = buscar_patron_cpin_ccel(archivo_modernizado, archivo_oracle, documentos_diferentes)
    
    # 4. Investigar lógica de negocio Oracle
    print(f"\n4️⃣ INVESTIGACIÓN LÓGICA DE NEGOCIO ORACLE:")
    investigar_logica_negocio_oracle(documentos_diferentes, archivo_oracle)
    
    # 5. Proponer solución ninja
    print(f"\n5️⃣ SOLUCIÓN NINJA:")
    proponer_solucion_ninja(patron_encontrado)

def identificar_documentos_cpin_ccel(archivo_modernizado, archivo_oracle):
    """Identifica documentos con diferencias CPIN vs CCEL"""
    try:
        # Leer archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        # Encontrar documentos comunes
        docs_mod = set(df_mod['DOCUMENTO'].astype(str))
        docs_oracle = set(df_oracle['DOCUMENTO'].astype(str))
        docs_comunes = docs_mod.intersection(docs_oracle)
        
        # Identificar diferencias CPIN vs CCEL
        documentos_diferentes = []
        
        for documento in docs_comunes:
            reg_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == documento]
            reg_oracle = df_oracle[df_oracle['DOCUMENTO'].astype(str) == documento]
            
            if len(reg_mod) > 0 and len(reg_oracle) > 0:
                mod_op = reg_mod.iloc[0]['OPERACION']
                oracle_op = reg_oracle.iloc[0]['OPERACION']
                
                # Buscar específicamente CPIN vs CCEL
                if (mod_op == 'CPIN' and oracle_op == 'CCEL') or (mod_op == 'CCEL' and oracle_op == 'CPIN'):
                    documentos_diferentes.append({
                        'documento': documento,
                        'modernizado_op': mod_op,
                        'oracle_op': oracle_op,
                        'modernizado_ts': reg_mod.iloc[0]['FECHA_HORA'],
                        'oracle_ts': reg_oracle.iloc[0]['FECHA_HORA'],
                        'celular': reg_mod.iloc[0]['CELULAR'],
                        'empresa': reg_mod.iloc[0]['EMPRESA']
                    })
        
        print(f"   📊 Documentos con diferencias CPIN vs CCEL: {len(documentos_diferentes)}")
        
        # Mostrar ejemplos
        for i, doc in enumerate(documentos_diferentes[:10], 1):
            print(f"      {i}. Doc {doc['documento']}:")
            print(f"         Modernizado: {doc['modernizado_op']} - {doc['modernizado_ts']}")
            print(f"         Oracle: {doc['oracle_op']} - {doc['oracle_ts']}")
            print(f"         Celular: {doc['celular']} - Empresa: {doc['empresa']}")
        
        if len(documentos_diferentes) > 10:
            print(f"      ... y {len(documentos_diferentes) - 10} más")
        
        return documentos_diferentes
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return []

def analizar_datos_originales_cpin_ccel(log_usr_path, documentos_diferentes):
    """Analiza los datos originales de documentos con diferencias CPIN vs CCEL"""
    try:
        if not documentos_diferentes:
            print(f"   ❌ No hay documentos para analizar")
            return
        
        conn = duckdb.connect()
        
        # Analizar los primeros 5 documentos en detalle
        documentos_muestra = documentos_diferentes[:5]
        
        print(f"   📊 Analizando muestra de {len(documentos_muestra)} documentos:")
        
        for doc_info in documentos_muestra:
            documento = doc_info['documento']
            
            print(f"\n   🔍 DOCUMENTO {documento}:")
            print(f"      Modernizado: {doc_info['modernizado_op']} - {doc_info['modernizado_ts']}")
            print(f"      Oracle: {doc_info['oracle_op']} - {doc_info['oracle_ts']}")
            
            # Obtener todos los registros originales
            query = f"""
            SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN
            FROM read_parquet('{log_usr_path}')
            WHERE DOCUMENTO = '{documento}'
            ORDER BY CREATEDON
            """
            
            result = conn.execute(query).fetchall()
            
            print(f"      📋 Registros originales ({len(result)}):")
            for i, (requesttype, createdon, userhistid, msisdn, bankdomain) in enumerate(result, 1):
                print(f"         {i}. {createdon} - {requesttype} - {userhistid}")
            
            # Analizar tipos de REQUESTTYPE disponibles
            requesttypes = [r[0] for r in result]
            requesttypes_unicos = list(set(requesttypes))
            
            print(f"      📊 REQUESTTYPE únicos: {requesttypes_unicos}")
            
            # Verificar si hay CHANGE_CELLPHONE
            if 'CHANGE_CELLPHONE' in requesttypes_unicos:
                print(f"      🔍 PATRÓN DETECTADO: Tiene CHANGE_CELLPHONE")
                print(f"         Oracle puede preferir CHANGE_CELLPHONE → CCEL")
            
            if 'CHANGE_AUTH_FACTOR' in requesttypes_unicos:
                print(f"      🔍 PATRÓN DETECTADO: Tiene CHANGE_AUTH_FACTOR")
                print(f"         Pipeline puede preferir CHANGE_AUTH_FACTOR → CPIN")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Error: {e}")

def buscar_patron_cpin_ccel(archivo_modernizado, archivo_oracle, documentos_diferentes):
    """Busca patrones en las diferencias CPIN vs CCEL"""
    try:
        if not documentos_diferentes:
            return None
        
        print(f"   🧠 ANÁLISIS DE PATRONES:")
        
        # Analizar patrones por timestamp
        diferencias_timestamp = 0
        mismos_timestamp = 0
        
        # Analizar patrones por empresa
        patrones_empresa = {}
        
        # Analizar patrones por horario
        patrones_horario = {'MAÑANA': 0, 'TARDE': 0, 'NOCHE': 0, 'MADRUGADA': 0}
        
        for doc in documentos_diferentes:
            # Timestamp
            if doc['modernizado_ts'] != doc['oracle_ts']:
                diferencias_timestamp += 1
            else:
                mismos_timestamp += 1
            
            # Empresa
            empresa = doc['empresa']
            if empresa not in patrones_empresa:
                patrones_empresa[empresa] = {'CPIN_to_CCEL': 0, 'CCEL_to_CPIN': 0}
            
            if doc['modernizado_op'] == 'CPIN' and doc['oracle_op'] == 'CCEL':
                patrones_empresa[empresa]['CPIN_to_CCEL'] += 1
            elif doc['modernizado_op'] == 'CCEL' and doc['oracle_op'] == 'CPIN':
                patrones_empresa[empresa]['CCEL_to_CPIN'] += 1
            
            # Horario
            try:
                dt = datetime.strptime(doc['oracle_ts'], '%Y-%m-%d %H:%M:%S')
                if 6 <= dt.hour < 12:
                    patrones_horario['MAÑANA'] += 1
                elif 12 <= dt.hour < 18:
                    patrones_horario['TARDE'] += 1
                elif 18 <= dt.hour < 24:
                    patrones_horario['NOCHE'] += 1
                else:
                    patrones_horario['MADRUGADA'] += 1
            except:
                pass
        
        total = len(documentos_diferentes)
        
        print(f"      📊 Timestamps:")
        print(f"         Diferentes: {diferencias_timestamp}/{total} ({diferencias_timestamp/total*100:.1f}%)")
        print(f"         Iguales: {mismos_timestamp}/{total} ({mismos_timestamp/total*100:.1f}%)")
        
        print(f"      📊 Patrones por empresa:")
        for empresa, patron in patrones_empresa.items():
            total_empresa = patron['CPIN_to_CCEL'] + patron['CCEL_to_CPIN']
            print(f"         {empresa}: CPIN→CCEL: {patron['CPIN_to_CCEL']}, CCEL→CPIN: {patron['CCEL_to_CPIN']} (Total: {total_empresa})")
        
        print(f"      📊 Patrones por horario:")
        for horario, count in patrones_horario.items():
            porcentaje = (count / total * 100) if total > 0 else 0
            print(f"         {horario}: {count}/{total} ({porcentaje:.1f}%)")
        
        # Determinar patrón principal
        patron = {
            'diferencias_timestamp': diferencias_timestamp > mismos_timestamp,
            'empresa_principal': max(patrones_empresa.keys(), key=lambda x: patrones_empresa[x]['CPIN_to_CCEL'] + patrones_empresa[x]['CCEL_to_CPIN']) if patrones_empresa else None,
            'horario_principal': max(patrones_horario.keys(), key=patrones_horario.get),
            'direccion_principal': 'CPIN_to_CCEL' if sum(p['CPIN_to_CCEL'] for p in patrones_empresa.values()) > sum(p['CCEL_to_CPIN'] for p in patrones_empresa.values()) else 'CCEL_to_CPIN'
        }
        
        return patron
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def investigar_logica_negocio_oracle(documentos_diferentes, archivo_oracle):
    """Investiga la lógica de negocio específica de Oracle"""
    try:
        print(f"   🔍 INVESTIGACIÓN LÓGICA DE NEGOCIO ORACLE:")
        
        if not documentos_diferentes:
            return
        
        # Leer archivo Oracle completo
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        # Analizar distribución de operaciones en Oracle
        operaciones_oracle = df_oracle['OPERACION'].value_counts()
        
        print(f"      📊 Distribución de operaciones en Oracle:")
        for op, count in operaciones_oracle.head(10).items():
            porcentaje = (count / len(df_oracle) * 100)
            print(f"         {op}: {count:,} ({porcentaje:.1f}%)")
        
        # Analizar si hay patrones por empresa
        print(f"      📊 Operaciones CCEL por empresa en Oracle:")
        ccel_por_empresa = df_oracle[df_oracle['OPERACION'] == 'CCEL']['EMPRESA'].value_counts()
        for empresa, count in ccel_por_empresa.head(5).items():
            print(f"         {empresa}: {count:,} registros CCEL")
        
        # Analizar si hay patrones por horario
        print(f"      📊 Operaciones CCEL por horario en Oracle:")
        df_oracle_ccel = df_oracle[df_oracle['OPERACION'] == 'CCEL'].copy()
        
        if len(df_oracle_ccel) > 0:
            df_oracle_ccel['HORA'] = pd.to_datetime(df_oracle_ccel['FECHA_HORA']).dt.hour
            ccel_por_hora = df_oracle_ccel['HORA'].value_counts().sort_index()
            
            for hora in range(0, 24, 6):
                rango_fin = min(hora + 5, 23)
                count = ccel_por_hora[hora:rango_fin+1].sum()
                periodo = 'MADRUGADA' if hora == 0 else 'MAÑANA' if hora == 6 else 'TARDE' if hora == 12 else 'NOCHE'
                print(f"         {periodo} ({hora:02d}-{rango_fin:02d}): {count:,} registros CCEL")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")

def proponer_solucion_ninja(patron):
    """Propone solución ninja basada en los patrones encontrados"""
    
    print(f"   🥷 SOLUCIÓN NINJA PROPUESTA:")
    
    if not patron:
        print(f"      ❌ No se pudo determinar patrón - requiere más investigación")
        return
    
    print(f"      🎯 PATRÓN IDENTIFICADO:")
    print(f"         Diferencias timestamp: {'Sí' if patron['diferencias_timestamp'] else 'No'}")
    print(f"         Empresa principal: {patron['empresa_principal']}")
    print(f"         Horario principal: {patron['horario_principal']}")
    print(f"         Dirección principal: {patron['direccion_principal']}")
    
    print(f"\n      💡 HIPÓTESIS NINJA:")
    
    if patron['direccion_principal'] == 'CPIN_to_CCEL':
        print(f"         Oracle prefiere CHANGE_CELLPHONE (CCEL) sobre CHANGE_AUTH_FACTOR (CPIN)")
        print(f"         cuando ambos están disponibles en el mismo documento")
        
        print(f"\n      🔧 SOLUCIÓN PROPUESTA:")
        print(f"         1. Modificar lógica de deduplicación para priorizar CHANGE_CELLPHONE")
        print(f"         2. Orden de prioridad: CHANGE_CELLPHONE > CHANGE_AUTH_FACTOR > otros")
        print(f"         3. Mantener lógica de horarios como criterio secundario")
        
        print(f"\n      📝 IMPLEMENTACIÓN:")
        print(f"         Agregar REQUESTTYPE como primer criterio de ordenamiento:")
        print(f"         ORDER BY")
        print(f"           CASE WHEN REQUESTTYPE = 'CHANGE_CELLPHONE' THEN 1")
        print(f"                WHEN REQUESTTYPE = 'CHANGE_AUTH_FACTOR' THEN 2")
        print(f"                ELSE 3 END ASC,")
        print(f"           periodo_prioridad ASC,")
        print(f"           CREATEDON ASC")
    
    else:
        print(f"         Patrón no estándar detectado - requiere investigación adicional")
        print(f"         Revisar lógica de negocio específica de Oracle")

if __name__ == "__main__":
    print("🥷 NINJA INVESTIGACIÓN CPIN vs CCEL")
    print("=" * 80)
    print("🎯 MISIÓN: Lograr 100% de homologación perfecta")
    
    ninja_investigacion_cpin_ccel()
    
    print(f"\n🏆 MISIÓN NINJA: Identificar y corregir diferencias CPIN vs CCEL")
    print(f"📋 OBJETIVO: Implementar solución para 100% homologación")
    
    print(f"\n✅ Investigación ninja completada")
