#!/usr/bin/env python3
"""
🥷 DATA CODE NINJA PRO - INVESTIGACIÓN FORENSE ORACLE LOGIC
Objetivo: Encontrar la lógica EXACTA que usa Oracle para seleccionar registros
No parar hasta lograr homologación "como dos gotas de agua"
"""
import pandas as pd
import duckdb
import os
from datetime import datetime

def ninja_investigacion_completa():
    """Investigación ninja completa de la lógica Oracle"""
    print("🥷 DATA CODE NINJA PRO - INVESTIGACIÓN FORENSE ORACLE")
    print("=" * 70)
    print("🎯 MISIÓN: Encontrar lógica EXACTA de Oracle")
    print("🎯 OBJETIVO: Homologación 'como dos gotas de agua'")
    
    # Documentos de prueba con comportamientos diferentes
    documentos_test = ["70635331", "76730654"]
    
    # Archivos
    log_usr_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"\n🔍 FASE 1: ANÁLISIS FORENSE DE PATRONES")
    patrones_oracle = analizar_patrones_oracle(archivo_oracle, documentos_test)
    
    print(f"\n🔍 FASE 2: ANÁLISIS DE DATOS ORIGINALES")
    datos_originales = analizar_datos_originales_detallado(log_usr_original, documentos_test)
    
    print(f"\n🔍 FASE 3: BÚSQUEDA DE LÓGICA OCULTA")
    logica_oculta = buscar_logica_oculta(datos_originales, patrones_oracle)
    
    print(f"\n🔍 FASE 4: ANÁLISIS DE MÚLTIPLES DOCUMENTOS")
    analizar_muestra_amplia(log_usr_original, archivo_oracle)
    
    print(f"\n🔍 FASE 5: INGENIERÍA REVERSA ORACLE")
    ingenieria_reversa_oracle(datos_originales, patrones_oracle)

def analizar_patrones_oracle(archivo_oracle, documentos):
    """Analiza patrones específicos en Oracle"""
    print("   🔬 ANÁLISIS FORENSE DE PATRONES ORACLE")
    
    try:
        df = pd.read_csv(archivo_oracle, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df.columns = columnas[:len(df.columns)]
        
        patrones = {}
        
        for doc in documentos:
            reg = df[df['DOCUMENTO'].astype(str) == doc]
            if len(reg) > 0:
                registro = reg.iloc[0]
                timestamp = registro['FECHA_HORA']
                
                # Extraer información detallada del timestamp
                dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                
                patrones[doc] = {
                    'timestamp': timestamp,
                    'hora': dt.hour,
                    'minuto': dt.minute,
                    'segundo': dt.second,
                    'es_madrugada': dt.hour < 6,
                    'es_manana': 6 <= dt.hour < 12,
                    'es_tarde': 12 <= dt.hour < 18,
                    'es_noche': dt.hour >= 18,
                    'transactionid': registro['TRANSACTIONID'],
                    'operacion': registro['OPERACION']
                }
                
                print(f"      📊 Doc {doc}: {timestamp}")
                print(f"         Hora: {dt.hour:02d}:{dt.minute:02d}:{dt.second:02d}")
                print(f"         Período: {'Madrugada' if dt.hour < 6 else 'Mañana' if dt.hour < 12 else 'Tarde' if dt.hour < 18 else 'Noche'}")
                print(f"         TID: {registro['TRANSACTIONID']}")
        
        return patrones
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {}

def analizar_datos_originales_detallado(log_usr_path, documentos):
    """Análisis detallado de datos originales"""
    print("   🔬 ANÁLISIS FORENSE DE DATOS ORIGINALES")
    
    try:
        conn = duckdb.connect()
        datos = {}
        
        for doc in documentos:
            query = f"""
            SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN
            FROM read_parquet('{log_usr_path}')
            WHERE DOCUMENTO = '{doc}'
            ORDER BY CREATEDON
            """
            
            result = conn.execute(query).fetchall()
            
            print(f"      📊 Doc {doc}: {len(result)} registros")
            
            registros_detalle = []
            for i, (requesttype, createdon, userhistid, msisdn, bankdomain) in enumerate(result, 1):
                dt = datetime.strptime(str(createdon), '%Y-%m-%d %H:%M:%S')

                detalle = {
                    'orden': i,
                    'timestamp': str(createdon),
                    'requesttype': requesttype,
                    'userhistid': userhistid,
                    'hora': dt.hour,
                    'minuto': dt.minute,
                    'segundo': dt.second,
                    'es_madrugada': dt.hour < 6,
                    'es_manana': 6 <= dt.hour < 12,
                    'es_tarde': 12 <= dt.hour < 18,
                    'es_noche': dt.hour >= 18,
                    'msisdn': msisdn,
                    'bankdomain': bankdomain
                }
                
                registros_detalle.append(detalle)
                
                periodo = 'Madrugada' if dt.hour < 6 else 'Mañana' if dt.hour < 12 else 'Tarde' if dt.hour < 18 else 'Noche'
                print(f"         {i}. {createdon} ({periodo}) - {userhistid}")
            
            datos[doc] = registros_detalle
        
        conn.close()
        return datos
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {}

def buscar_logica_oculta(datos_originales, patrones_oracle):
    """Busca lógica oculta comparando datos originales vs Oracle"""
    print("   🔬 BÚSQUEDA DE LÓGICA OCULTA")
    
    for doc in datos_originales.keys():
        if doc not in patrones_oracle:
            continue
            
        registros = datos_originales[doc]
        oracle_timestamp = patrones_oracle[doc]['timestamp']
        oracle_hora = patrones_oracle[doc]['hora']
        
        print(f"      📊 Doc {doc}:")
        print(f"         Oracle selecciona: {oracle_timestamp}")
        
        # Buscar qué registro coincide con Oracle
        registro_oracle = None
        for reg in registros:
            if reg['timestamp'] == oracle_timestamp:
                registro_oracle = reg
                break
        
        if registro_oracle:
            print(f"         Registro Oracle es el #{registro_oracle['orden']} de {len(registros)}")
            
            # Analizar patrones
            if len(registros) > 1:
                print(f"         🔍 ANÁLISIS DE PATRÓN:")
                
                # ¿Es el primero o último?
                if registro_oracle['orden'] == 1:
                    print(f"            ✅ Oracle selecciona el PRIMERO (más antiguo)")
                elif registro_oracle['orden'] == len(registros):
                    print(f"            ✅ Oracle selecciona el ÚLTIMO (más reciente)")
                else:
                    print(f"            ⚠️  Oracle selecciona el #{registro_oracle['orden']} (ni primero ni último)")
                
                # ¿Hay patrón por período del día?
                periodos = [r['es_madrugada'] for r in registros]
                if registro_oracle['es_madrugada'] and any(not p for p in periodos):
                    print(f"            🌙 Oracle prefiere MADRUGADA sobre otros períodos")
                elif registro_oracle['es_manana'] and any(r['es_madrugada'] for r in registros):
                    print(f"            🌅 Oracle prefiere MAÑANA sobre MADRUGADA")
                elif registro_oracle['es_noche']:
                    print(f"            🌃 Oracle selecciona NOCHE")
                
                # ¿Hay patrón por hora específica?
                horas = [r['hora'] for r in registros]
                hora_min = min(horas)
                hora_max = max(horas)
                
                if registro_oracle['hora'] == hora_min:
                    print(f"            ⏰ Oracle selecciona la HORA MÁS TEMPRANA ({registro_oracle['hora']})")
                elif registro_oracle['hora'] == hora_max:
                    print(f"            ⏰ Oracle selecciona la HORA MÁS TARDÍA ({registro_oracle['hora']})")
                
                # Buscar otros patrones
                print(f"            📊 Estadísticas:")
                print(f"               Horas disponibles: {sorted(set(horas))}")
                print(f"               Oracle eligió: {registro_oracle['hora']}")

def analizar_muestra_amplia(log_usr_path, archivo_oracle):
    """Analiza una muestra más amplia para encontrar patrones"""
    print("   🔬 ANÁLISIS DE MUESTRA AMPLIA")
    
    try:
        # Leer Oracle
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        # Tomar muestra de documentos
        documentos_muestra = df_oracle['DOCUMENTO'].astype(str).unique()[:20]
        
        conn = duckdb.connect()
        
        patrones_encontrados = {
            'selecciona_primero': 0,
            'selecciona_ultimo': 0,
            'selecciona_medio': 0,
            'prefiere_madrugada': 0,
            'prefiere_manana': 0,
            'prefiere_tarde': 0,
            'prefiere_noche': 0
        }
        
        documentos_analizados = 0
        
        for doc in documentos_muestra:
            # Datos originales
            query = f"""
            SELECT REQUESTTYPE, CREATEDON, USERHISTID
            FROM read_parquet('{log_usr_path}')
            WHERE DOCUMENTO = '{doc}'
            ORDER BY CREATEDON
            """
            
            result = conn.execute(query).fetchall()
            
            if len(result) <= 1:
                continue  # Solo analizar documentos con múltiples registros
            
            # Oracle
            oracle_reg = df_oracle[df_oracle['DOCUMENTO'].astype(str) == doc]
            if len(oracle_reg) == 0:
                continue
            
            oracle_timestamp = oracle_reg.iloc[0]['FECHA_HORA']
            oracle_dt = datetime.strptime(oracle_timestamp, '%Y-%m-%d %H:%M:%S')
            
            # Encontrar posición del registro Oracle
            timestamps = [str(r[1]) for r in result]
            horas = [datetime.strptime(str(r[1]), '%Y-%m-%d %H:%M:%S').hour for r in result]
            
            if oracle_timestamp in timestamps:
                posicion = timestamps.index(oracle_timestamp) + 1
                total = len(timestamps)
                
                if posicion == 1:
                    patrones_encontrados['selecciona_primero'] += 1
                elif posicion == total:
                    patrones_encontrados['selecciona_ultimo'] += 1
                else:
                    patrones_encontrados['selecciona_medio'] += 1
                
                # Analizar período
                if oracle_dt.hour < 6:
                    patrones_encontrados['prefiere_madrugada'] += 1
                elif oracle_dt.hour < 12:
                    patrones_encontrados['prefiere_manana'] += 1
                elif oracle_dt.hour < 18:
                    patrones_encontrados['prefiere_tarde'] += 1
                else:
                    patrones_encontrados['prefiere_noche'] += 1
                
                documentos_analizados += 1
        
        print(f"      📊 Documentos analizados: {documentos_analizados}")
        print(f"      📊 PATRONES ENCONTRADOS:")
        for patron, count in patrones_encontrados.items():
            porcentaje = (count / documentos_analizados * 100) if documentos_analizados > 0 else 0
            print(f"         {patron}: {count} ({porcentaje:.1f}%)")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Error: {e}")

def ingenieria_reversa_oracle(datos_originales, patrones_oracle):
    """Ingeniería reversa para determinar la lógica exacta"""
    print("   🔬 INGENIERÍA REVERSA ORACLE")
    
    print(f"      🎯 HIPÓTESIS A VERIFICAR:")
    print(f"         1. Oracle usa lógica por período del día")
    print(f"         2. Oracle tiene preferencias por horarios específicos")
    print(f"         3. Oracle usa criterios de negocio ocultos")
    print(f"         4. Oracle tiene lógica condicional compleja")
    
    # Analizar cada documento en detalle
    for doc in datos_originales.keys():
        if doc not in patrones_oracle:
            continue
            
        print(f"      📊 INGENIERÍA REVERSA DOC {doc}:")
        
        registros = datos_originales[doc]
        oracle_timestamp = patrones_oracle[doc]['timestamp']
        
        # Encontrar registro Oracle
        registro_oracle = None
        for i, reg in enumerate(registros):
            if reg['timestamp'] == oracle_timestamp:
                registro_oracle = reg
                break
        
        if registro_oracle and len(registros) > 1:
            print(f"         Oracle eligió: {oracle_timestamp}")
            print(f"         Posición: #{registro_oracle['orden']} de {len(registros)}")
            
            # Generar hipótesis específicas
            if registro_oracle['orden'] == 1:
                print(f"         💡 HIPÓTESIS: Oracle prefiere el MÁS ANTIGUO para este caso")
            elif registro_oracle['orden'] == len(registros):
                print(f"         💡 HIPÓTESIS: Oracle prefiere el MÁS RECIENTE para este caso")
            
            # Analizar diferencias entre registros
            horas_disponibles = [r['hora'] for r in registros]
            if len(set(horas_disponibles)) > 1:
                if registro_oracle['hora'] == min(horas_disponibles):
                    print(f"         💡 HIPÓTESIS: Oracle prefiere la HORA MÁS TEMPRANA")
                elif registro_oracle['hora'] == max(horas_disponibles):
                    print(f"         💡 HIPÓTESIS: Oracle prefiere la HORA MÁS TARDÍA")

if __name__ == "__main__":
    print("🥷 DATA CODE NINJA PRO - MISIÓN INICIADA")
    print("=" * 80)
    
    ninja_investigacion_completa()
    
    print(f"\n🎯 MISIÓN NINJA: ENCONTRAR LÓGICA EXACTA ORACLE")
    print(f"📋 OBJETIVO: HOMOLOGACIÓN 'COMO DOS GOTAS DE AGUA'")
    
    print(f"\n✅ Investigación ninja completada")
