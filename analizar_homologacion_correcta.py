#!/usr/bin/env python3
"""
Script para analizar la homologación correcta entre pipeline y Oracle
TransactionID puede ser diferente, pero el resto de datos debe coincidir
"""
import pandas as pd
import os

def analizar_homologacion_correcta():
    """Analiza la homologación correcta excluyendo TransactionID"""
    print("🔍 ANÁLISIS DE HOMOLOGACIÓN CORRECTA")
    print("=" * 50)
    
    documento_buscar = "76730654"
    
    # Archivos a comparar - usar el más reciente (con corrección aplicada)
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609195143.csv"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"🎯 Documento a analizar: {documento_buscar}")
    print(f"💡 TransactionID puede ser diferente (lógica adicional)")
    print(f"✅ El resto de datos DEBE coincidir (DiaHora, Operación, etc.)")
    
    # Analizar ambos archivos
    registros_modernizado = extraer_registros_documento(archivo_modernizado, documento_buscar, "MODERNIZADO")
    registros_oracle = extraer_registros_documento(archivo_oracle, documento_buscar, "ORACLE")
    
    # Comparación de homologación
    print(f"\n🔍 ANÁLISIS DE HOMOLOGACIÓN:")
    analizar_homologacion_detallada(registros_modernizado, registros_oracle, documento_buscar)
    
    # Análisis de múltiples documentos para confirmar patrón
    print(f"\n🔍 ANÁLISIS DE PATRÓN EN MÚLTIPLES DOCUMENTOS:")
    analizar_patron_multiple_documentos(archivo_modernizado, archivo_oracle)

def extraer_registros_documento(archivo_path, documento, tipo_archivo):
    """Extrae registros de un documento específico"""
    try:
        df = pd.read_csv(archivo_path, header=None)
        
        # Asignar nombres de columnas
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df.columns = columnas[:len(df.columns)]
        
        # Buscar documento específico
        mask_documento = df['DOCUMENTO'].astype(str) == documento
        registros_documento = df[mask_documento]
        
        print(f"\n📊 {tipo_archivo}:")
        print(f"   • Total registros archivo: {len(df):,}")
        print(f"   • Registros documento {documento}: {len(registros_documento)}")
        
        if len(registros_documento) > 0:
            registros_detalle = []
            for _, registro in registros_documento.iterrows():
                # Crear clave de negocio (sin TransactionID)
                clave_negocio = f"{registro['OPERACION']}|{registro['FECHA_HORA']}|{registro['DOCUMENTO']}|{registro['CELULAR']}|{registro['EMPRESA']}"
                
                registros_detalle.append({
                    'clave_negocio': clave_negocio,
                    'operacion': registro['OPERACION'],
                    'fecha_hora': registro['FECHA_HORA'],
                    'transactionid': registro['TRANSACTIONID'],
                    'documento': registro['DOCUMENTO'],
                    'celular': registro['CELULAR'],
                    'empresa': registro['EMPRESA'],
                    'canal': registro['CANAL'],
                    'tipodocumento': registro['TIPODOCUMENTO'],
                    'registro_completo': registro
                })
                
                print(f"      {registro['OPERACION']} - {registro['FECHA_HORA']} - TID: {registro['TRANSACTIONID']}")
            
            return registros_detalle
        else:
            print(f"   ❌ Documento no encontrado")
            return []
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return []

def analizar_homologacion_detallada(registros_modernizado, registros_oracle, documento):
    """Analiza la homologación detallada"""
    
    print(f"   📊 Resumen:")
    print(f"      • Modernizado: {len(registros_modernizado)} registros")
    print(f"      • Oracle: {len(registros_oracle)} registros")
    
    if len(registros_modernizado) == 0 or len(registros_oracle) == 0:
        print(f"   ❌ No se puede comparar - falta documento en uno de los archivos")
        return
    
    # Crear sets de claves de negocio (sin TransactionID)
    claves_modernizado = set(reg['clave_negocio'] for reg in registros_modernizado)
    claves_oracle = set(reg['clave_negocio'] for reg in registros_oracle)
    
    print(f"\n   🔍 ANÁLISIS DE CLAVES DE NEGOCIO:")
    print(f"      (OPERACION|FECHA_HORA|DOCUMENTO|CELULAR|EMPRESA)")
    
    # Claves comunes
    claves_comunes = claves_modernizado & claves_oracle
    print(f"      ✅ Claves comunes: {len(claves_comunes)}")
    
    # Claves solo en modernizado
    solo_modernizado = claves_modernizado - claves_oracle
    print(f"      🔵 Solo en modernizado: {len(solo_modernizado)}")
    
    # Claves solo en Oracle
    solo_oracle = claves_oracle - claves_modernizado
    print(f"      🔴 Solo en Oracle: {len(solo_oracle)}")
    
    # Mostrar detalles de diferencias
    if solo_modernizado:
        print(f"\n   🔵 REGISTROS SOLO EN MODERNIZADO:")
        for clave in solo_modernizado:
            reg = next(r for r in registros_modernizado if r['clave_negocio'] == clave)
            print(f"      • {reg['operacion']} - {reg['fecha_hora']} - TID: {reg['transactionid']}")
    
    if solo_oracle:
        print(f"\n   🔴 REGISTROS SOLO EN ORACLE:")
        for clave in solo_oracle:
            reg = next(r for r in registros_oracle if r['clave_negocio'] == clave)
            print(f"      • {reg['operacion']} - {reg['fecha_hora']} - TID: {reg['transactionid']}")
    
    if claves_comunes:
        print(f"\n   ✅ REGISTROS HOMOLOGADOS CORRECTAMENTE:")
        for clave in claves_comunes:
            reg_mod = next(r for r in registros_modernizado if r['clave_negocio'] == clave)
            reg_ora = next(r for r in registros_oracle if r['clave_negocio'] == clave)
            print(f"      • {reg_mod['operacion']} - {reg_mod['fecha_hora']}")
            print(f"        Modernizado TID: {reg_mod['transactionid']}")
            print(f"        Oracle TID: {reg_ora['transactionid']}")
    
    # Conclusión para este documento
    if len(claves_comunes) == len(registros_modernizado) == len(registros_oracle):
        print(f"\n   🏆 HOMOLOGACIÓN PERFECTA para documento {documento}")
        print(f"      ✅ Todos los registros coinciden (excepto TransactionID)")
    elif len(claves_comunes) > 0:
        print(f"\n   ⚠️  HOMOLOGACIÓN PARCIAL para documento {documento}")
        print(f"      ✅ {len(claves_comunes)} registros homologados")
        print(f"      ❌ {len(solo_modernizado)} registros extra en modernizado")
        print(f"      ❌ {len(solo_oracle)} registros extra en Oracle")
    else:
        print(f"\n   ❌ SIN HOMOLOGACIÓN para documento {documento}")
        print(f"      💡 Los registros representan eventos diferentes")

def analizar_patron_multiple_documentos(archivo_modernizado, archivo_oracle):
    """Analiza el patrón en múltiples documentos para confirmar comportamiento"""
    
    try:
        # Leer ambos archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_ora = pd.read_csv(archivo_oracle, header=None)
        
        # Asignar columnas
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_ora.columns = columnas[:len(df_ora.columns)]
        
        print(f"   📊 Análisis de muestra de documentos:")
        
        # Tomar una muestra de documentos que aparecen en ambos archivos
        docs_mod = set(df_mod['DOCUMENTO'].astype(str))
        docs_ora = set(df_ora['DOCUMENTO'].astype(str))
        docs_comunes = docs_mod & docs_ora
        
        print(f"      • Documentos en modernizado: {len(docs_mod):,}")
        print(f"      • Documentos en Oracle: {len(docs_ora):,}")
        print(f"      • Documentos comunes: {len(docs_comunes):,}")
        
        # Analizar muestra de 10 documentos comunes
        muestra_docs = list(docs_comunes)[:10]
        
        homologacion_perfecta = 0
        homologacion_parcial = 0
        sin_homologacion = 0
        
        for doc in muestra_docs:
            # Registros del documento en ambos archivos
            regs_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == doc]
            regs_ora = df_ora[df_ora['DOCUMENTO'].astype(str) == doc]
            
            # Crear claves de negocio
            claves_mod = set()
            claves_ora = set()
            
            for _, reg in regs_mod.iterrows():
                clave = f"{reg['OPERACION']}|{reg['FECHA_HORA']}|{reg['DOCUMENTO']}|{reg['CELULAR']}|{reg['EMPRESA']}"
                claves_mod.add(clave)
            
            for _, reg in regs_ora.iterrows():
                clave = f"{reg['OPERACION']}|{reg['FECHA_HORA']}|{reg['DOCUMENTO']}|{reg['CELULAR']}|{reg['EMPRESA']}"
                claves_ora.add(clave)
            
            # Evaluar homologación
            claves_comunes = claves_mod & claves_ora
            
            if len(claves_comunes) == len(claves_mod) == len(claves_ora) and len(claves_comunes) > 0:
                homologacion_perfecta += 1
            elif len(claves_comunes) > 0:
                homologacion_parcial += 1
            else:
                sin_homologacion += 1
        
        print(f"\n   📊 RESULTADOS DE MUESTRA ({len(muestra_docs)} documentos):")
        print(f"      🏆 Homologación perfecta: {homologacion_perfecta} documentos")
        print(f"      ⚠️  Homologación parcial: {homologacion_parcial} documentos")
        print(f"      ❌ Sin homologación: {sin_homologacion} documentos")
        
        # Calcular porcentaje
        total_analizados = len(muestra_docs)
        if total_analizados > 0:
            porcentaje_perfecto = (homologacion_perfecta / total_analizados) * 100
            porcentaje_parcial = (homologacion_parcial / total_analizados) * 100
            
            print(f"\n   📊 PORCENTAJES:")
            print(f"      🏆 Homologación perfecta: {porcentaje_perfecto:.1f}%")
            print(f"      ⚠️  Homologación parcial: {porcentaje_parcial:.1f}%")
            print(f"      ❌ Sin homologación: {100 - porcentaje_perfecto - porcentaje_parcial:.1f}%")
        
    except Exception as e:
        print(f"   ❌ Error en análisis múltiple: {e}")

if __name__ == "__main__":
    print("🕵️ ANÁLISIS DE HOMOLOGACIÓN CORRECTA (EXCLUYENDO TRANSACTIONID)")
    print("=" * 80)
    
    analizar_homologacion_correcta()
    
    print(f"\n📋 CONCLUSIONES:")
    print(f"   • TransactionID puede ser diferente (lógica adicional)")
    print(f"   • El resto de datos debe coincidir para homologación correcta")
    print(f"   • Diferencias en DiaHora indican eventos de negocio diferentes")
    
    print(f"\n✅ Análisis completado")
