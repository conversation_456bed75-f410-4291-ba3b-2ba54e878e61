#!/usr/bin/env python3
"""
Investigación específica del documento 76730654 para lograr homologación exacta
"""
import pandas as pd
import os

def investigar_documento_76730654():
    """Investiga específicamente por qué el documento 76730654 no homologa"""
    print("🔍 INVESTIGACIÓN ESPECÍFICA DOCUMENTO 76730654")
    print("=" * 55)
    
    documento = "76730654"

    # Archivos a comparar
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********192147.csv"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"🎯 Documento objetivo: {documento}")
    print(f"📄 Archivo modernizado: {os.path.basename(archivo_modernizado)}")
    print(f"📄 Archivo Oracle: {os.path.basename(archivo_oracle)}")
    
    # 1. Verificar registros en archivos temporales
    print(f"\n1️⃣ RASTREO EN ARCHIVOS TEMPORALES:")
    rastrear_en_temporales_detallado(documento)
    
    # 2. Analizar el registro específico en ambos archivos
    print(f"\n2️⃣ ANÁLISIS ESPECÍFICO EN ARCHIVOS FINALES:")
    analizar_registro_especifico(archivo_modernizado, archivo_oracle, documento)
    
    # 3. Investigar por qué Oracle tiene 09:41:55 y no 00:52:48
    print(f"\n3️⃣ INVESTIGACIÓN DE ORIGEN DE DATOS:")
    investigar_origen_datos(documento)
    
    # 4. Proponer solución específica
    print(f"\n4️⃣ PROPUESTA DE SOLUCIÓN:")
    proponer_solucion_especifica(documento)

def rastrear_en_temporales_detallado(documento):
    """Rastrea el documento en archivos temporales con más detalle"""
    
    # Verificar USER_AUTH_CHANGE_HISTORY
    archivo_auth = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet"
    
    if os.path.exists(archivo_auth):
        try:
            df = pd.read_parquet(archivo_auth)
            print(f"   📄 USER_AUTH_CHANGE_HISTORY.parquet:")
            print(f"      Total registros: {len(df):,}")
            
            # Buscar por AUTHENTICATION_ID que contenga el USER_ID del documento
            # Primero necesitamos encontrar el USER_ID del documento
            archivo_user_data = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet"
            
            if os.path.exists(archivo_user_data):
                df_user = pd.read_parquet(archivo_user_data)
                mask_user = df_user['ID_VALUE'].astype(str) == documento
                user_info = df_user[mask_user]
                
                if len(user_info) > 0:
                    user_id = user_info.iloc[0]['USER_ID']
                    o_user_id = user_info.iloc[0]['O_USER_ID']
                    
                    print(f"      🎯 Usuario encontrado:")
                    print(f"         USER_ID: {user_id}")
                    print(f"         O_USER_ID: {o_user_id}")
                    
                    # Buscar CHANGE_AUTH_FACTOR para este usuario
                    # Los AUTHENTICATION_ID suelen tener formato AU.SU{USER_ID}
                    auth_pattern = f"AU.SU{user_id}"
                    
                    mask_auth = (df['MODIFICATION_TYPE'] == 'CHANGE_AUTH_FACTOR') & \
                               (df['AUTHENTICATION_ID'].astype(str).str.contains(str(user_id), na=False))
                    
                    auth_records = df[mask_auth]
                    
                    print(f"      🎯 CHANGE_AUTH_FACTOR para usuario:")
                    print(f"         Total registros: {len(auth_records)}")
                    
                    if len(auth_records) > 0:
                        print(f"         📋 Registros encontrados:")
                        for _, reg in auth_records.iterrows():
                            print(f"            {reg['MODIFIED_ON']} - {reg['AUTHENTICATION_ID']}")
                    else:
                        print(f"         ❌ No se encontraron registros CHANGE_AUTH_FACTOR")
                        
                        # Buscar cualquier registro con el USER_ID
                        mask_any = df['AUTHENTICATION_ID'].astype(str).str.contains(str(user_id), na=False)
                        any_records = df[mask_any]
                        
                        print(f"         🔍 Cualquier registro con USER_ID {user_id}: {len(any_records)}")
                        if len(any_records) > 0:
                            for _, reg in any_records.iterrows():
                                print(f"            {reg['MODIFIED_ON']} - {reg['MODIFICATION_TYPE']} - {reg['AUTHENTICATION_ID']}")
            
        except Exception as e:
            print(f"      ❌ Error: {e}")

def analizar_registro_especifico(archivo_modernizado, archivo_oracle, documento):
    """Analiza el registro específico en ambos archivos"""
    
    try:
        # Leer archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_ora = pd.read_csv(archivo_oracle, header=None)
        
        # Asignar columnas
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_ora.columns = columnas[:len(df_ora.columns)]
        
        # Buscar documento específico
        reg_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == documento]
        reg_ora = df_ora[df_ora['DOCUMENTO'].astype(str) == documento]
        
        print(f"   📊 Análisis detallado:")
        print(f"      📄 Modernizado:")
        if len(reg_mod) > 0:
            for _, reg in reg_mod.iterrows():
                print(f"         {reg['OPERACION']} - {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
                print(f"         Canal: {reg['CANAL']} - Empresa: {reg['EMPRESA']}")
                print(f"         Celular: {reg['CELULAR']}")
        else:
            print(f"         ❌ No encontrado")
        
        print(f"      📄 Oracle:")
        if len(reg_ora) > 0:
            for _, reg in reg_ora.iterrows():
                print(f"         {reg['OPERACION']} - {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
                print(f"         Canal: {reg['CANAL']} - Empresa: {reg['EMPRESA']}")
                print(f"         Celular: {reg['CELULAR']}")
        else:
            print(f"         ❌ No encontrado")
        
        # Comparar todos los campos excepto TransactionID y FECHA_HORA
        if len(reg_mod) > 0 and len(reg_ora) > 0:
            reg_m = reg_mod.iloc[0]
            reg_o = reg_ora.iloc[0]
            
            print(f"\n   🔍 Comparación campo por campo:")
            campos_comparar = ['OPERACION', 'CANAL', 'TIPODOCUMENTO', 'DOCUMENTO', 'CELULAR', 'EMPRESA']
            
            for campo in campos_comparar:
                val_mod = reg_m[campo]
                val_ora = reg_o[campo]
                
                if val_mod == val_ora:
                    print(f"      ✅ {campo}: {val_mod}")
                else:
                    print(f"      ❌ {campo}: MOD={val_mod} vs ORA={val_ora}")
            
            print(f"      🕐 FECHA_HORA: MOD={reg_m['FECHA_HORA']} vs ORA={reg_o['FECHA_HORA']}")
            print(f"      🔢 TRANSACTIONID: MOD={reg_m['TRANSACTIONID']} vs ORA={reg_o['TRANSACTIONID']} (esperado diferente)")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")

def investigar_origen_datos(documento):
    """Investiga el origen de los datos para entender las diferencias"""
    
    print(f"   🔍 Investigando origen de datos...")
    
    # Verificar LOG_USR.parquet original
    archivo_log_usr = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/********/LOG_USR.parquet"
    
    if os.path.exists(archivo_log_usr):
        try:
            df = pd.read_parquet(archivo_log_usr)
            
            # Buscar todos los registros del documento
            mask = df['DOCUMENTO'].astype(str) == documento
            registros = df[mask]
            
            print(f"   📄 LOG_USR.parquet original:")
            print(f"      🎯 Registros del documento: {len(registros)}")
            
            if len(registros) > 0:
                print(f"      📋 Todos los registros:")
                for i, (_, reg) in enumerate(registros.iterrows(), 1):
                    print(f"         {i}. {reg['REQUESTTYPE']} - {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                
                # Verificar si hay múltiples CHANGE_AUTH_FACTOR
                change_auth = registros[registros['REQUESTTYPE'] == 'CHANGE_AUTH_FACTOR']
                print(f"      🎯 CHANGE_AUTH_FACTOR específicos: {len(change_auth)}")
                
                if len(change_auth) > 0:
                    print(f"         📋 Detalles CHANGE_AUTH_FACTOR:")
                    for i, (_, reg) in enumerate(change_auth.iterrows(), 1):
                        print(f"            {i}. {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                        print(f"               MSISDN: {reg['MSISDN']} - BankDomain: {reg['BANKDOMAIN']}")
                
                # Verificar archivo deduplicado
                archivo_dedup = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/********/LOG_USR_DEDUPLICATED.parquet"
                
                if os.path.exists(archivo_dedup):
                    df_dedup = pd.read_parquet(archivo_dedup)
                    mask_dedup = df_dedup['DOCUMENTO'].astype(str) == documento
                    registros_dedup = df_dedup[mask_dedup]
                    
                    print(f"\n   📄 LOG_USR_DEDUPLICATED.parquet:")
                    print(f"      🎯 Registros después de deduplicación: {len(registros_dedup)}")
                    
                    if len(registros_dedup) > 0:
                        for _, reg in registros_dedup.iterrows():
                            print(f"         {reg['REQUESTTYPE']} - {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                    
                    # Verificar qué se eliminó
                    if len(registros) > len(registros_dedup):
                        print(f"      ⚠️  Se eliminaron {len(registros) - len(registros_dedup)} registros en deduplicación")
                        
                        # Mostrar qué registros se eliminaron
                        userhistids_orig = set(registros['USERHISTID'])
                        userhistids_dedup = set(registros_dedup['USERHISTID'])
                        eliminados = userhistids_orig - userhistids_dedup
                        
                        if eliminados:
                            print(f"         🗑️  UserHistIds eliminados: {eliminados}")
                            
                            # Mostrar detalles de los eliminados
                            for userhistid in eliminados:
                                reg_eliminado = registros[registros['USERHISTID'] == userhistid].iloc[0]
                                print(f"            Eliminado: {reg_eliminado['REQUESTTYPE']} - {reg_eliminado['CREATEDON']}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")

def proponer_solucion_especifica(documento):
    """Propone solución específica para este documento"""
    
    print(f"   💡 Análisis de la situación:")
    print(f"      • El documento {documento} tiene múltiples eventos CHANGE_AUTH_FACTOR")
    print(f"      • Pipeline modernizado toma el de 00:52:48 (más antiguo)")
    print(f"      • Oracle tiene el de 09:41:55 (más reciente)")
    print(f"      • Esto indica diferentes criterios de selección")
    
    print(f"\n   🔧 Posibles soluciones:")
    print(f"      1. CAMBIAR CRITERIO DE DEDUPLICACIÓN:")
    print(f"         - Volver a usar CREATEDON DESC para tomar el más reciente")
    print(f"         - Esto haría que coincida con Oracle")
    
    print(f"      2. INVESTIGAR LÓGICA DE ORACLE:")
    print(f"         - Verificar exactamente qué criterio usa Oracle")
    print(f"         - Puede que Oracle use un filtro adicional")
    
    print(f"      3. VERIFICAR DATOS DE ORIGEN:")
    print(f"         - Confirmar si ambos eventos existen en las tablas fuente")
    print(f"         - Verificar si hay diferencias en ventanas de procesamiento")
    
    print(f"      4. AJUSTE ESPECÍFICO:")
    print(f"         - Implementar la misma lógica exacta que Oracle")
    print(f"         - Puede requerir análisis del código Oracle original")
    
    print(f"\n   🎯 RECOMENDACIÓN INMEDIATA:")
    print(f"      Probar cambiar la deduplicación de ASC a DESC para ver si")
    print(f"      el documento {documento} homologa correctamente")

if __name__ == "__main__":
    print("🕵️ INVESTIGACIÓN ESPECÍFICA - DOCUMENTO 76730654")
    print("=" * 70)
    
    investigar_documento_76730654()
    
    print(f"\n📋 CONCLUSIÓN:")
    print(f"   El documento 76730654 es un caso específico que requiere")
    print(f"   ajuste en la lógica de deduplicación para homologar exactamente")
    print(f"   con Oracle. La diferencia está en qué registro se selecciona")
    print(f"   cuando hay múltiples eventos del mismo tipo.")
    
    print(f"\n✅ Investigación completada")
