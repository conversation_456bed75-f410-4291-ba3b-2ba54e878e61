#!/usr/bin/env python3
"""
Análisis detective profundo de dos documentos específicos para verificar homologación exacta
Documentos: 70635331 y 76730654
Objetivo: Verificar si cuadran "como dos gotas de agua" con Oracle
"""
import pandas as pd
import duckdb
import os

def analisis_detective_completo():
    """Análisis detective completo de los dos documentos"""
    print("🕵️ ANÁLISIS DETECTIVE PROFUNDO - DOS DOCUMENTOS CRÍTICOS")
    print("=" * 70)
    
    documentos = ["70635331", "76730654"]
    
    # Archivos
    log_usr_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609195143.csv"
    
    print(f"🎯 Documentos a analizar: {documentos}")
    print(f"📄 LOG_USR original: {os.path.basename(log_usr_original)}")
    print(f"📄 Oracle: {os.path.basename(archivo_oracle)}")
    print(f"📄 Modernizado: {os.path.basename(archivo_modernizado)}")
    
    for documento in documentos:
        print(f"\n{'='*50}")
        print(f"🔍 ANÁLISIS DOCUMENTO: {documento}")
        print(f"{'='*50}")
        
        # 1. Análisis en datos originales
        print(f"\n1️⃣ DATOS ORIGINALES EN LOG_USR.parquet:")
        analizar_datos_originales(log_usr_original, documento)
        
        # 2. Análisis con deduplicación ASC
        print(f"\n2️⃣ DEDUPLICACIÓN ASC (más antiguo):")
        resultado_asc = analizar_con_deduplicacion(log_usr_original, documento, "ASC")
        
        # 3. Análisis con deduplicación DESC
        print(f"\n3️⃣ DEDUPLICACIÓN DESC (más reciente):")
        resultado_desc = analizar_con_deduplicacion(log_usr_original, documento, "DESC")
        
        # 4. Análisis en Oracle
        print(f"\n4️⃣ RESULTADO EN ORACLE:")
        resultado_oracle = analizar_en_oracle(archivo_oracle, documento)
        
        # 5. Análisis en archivo modernizado actual
        print(f"\n5️⃣ RESULTADO EN MODERNIZADO ACTUAL:")
        resultado_modernizado = analizar_en_modernizado(archivo_modernizado, documento)
        
        # 6. Comparación y conclusión
        print(f"\n6️⃣ COMPARACIÓN Y CONCLUSIÓN:")
        comparar_resultados(documento, resultado_asc, resultado_desc, resultado_oracle, resultado_modernizado)

def analizar_datos_originales(log_usr_path, documento):
    """Analiza los datos originales sin deduplicación"""
    try:
        conn = duckdb.connect()
        
        query = f"""
        SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN
        FROM read_parquet('{log_usr_path}')
        WHERE DOCUMENTO = '{documento}'
        ORDER BY CREATEDON
        """
        
        result = conn.execute(query).fetchall()
        
        print(f"   📊 Total registros: {len(result)}")
        
        if len(result) > 0:
            print(f"   📋 Todos los registros:")
            for i, (requesttype, createdon, userhistid, msisdn, bankdomain) in enumerate(result, 1):
                print(f"      {i}. {requesttype} - {createdon} - {userhistid}")
                print(f"         MSISDN: {msisdn} - BankDomain: {bankdomain}")
        else:
            print(f"   ❌ No se encontraron registros")
            
        conn.close()
        return result
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return []

def analizar_con_deduplicacion(log_usr_path, documento, orden):
    """Analiza con deduplicación específica (ASC o DESC)"""
    try:
        conn = duckdb.connect()
        
        dedup_query = f"""
        CREATE OR REPLACE TABLE log_usr_test AS
        SELECT DISTINCT ON (USERHISTID, REQUESTTYPE) *
        FROM read_parquet('{log_usr_path}')
        ORDER BY USERHISTID, REQUESTTYPE, CREATEDON {orden}
        """
        
        conn.execute(dedup_query)
        
        query = f"""
        SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN
        FROM log_usr_test
        WHERE DOCUMENTO = '{documento}'
        """
        
        result = conn.execute(query).fetchall()
        
        print(f"   📊 Registros después de deduplicación {orden}: {len(result)}")
        
        if len(result) > 0:
            for requesttype, createdon, userhistid, msisdn, bankdomain in result:
                print(f"   📋 Registro seleccionado:")
                print(f"      • {requesttype} - {createdon} - {userhistid}")
                print(f"        MSISDN: {msisdn} - BankDomain: {bankdomain}")
                
                conn.close()
                return {
                    'timestamp': str(createdon),
                    'requesttype': requesttype,
                    'userhistid': userhistid,
                    'msisdn': msisdn,
                    'bankdomain': bankdomain
                }
        else:
            print(f"   ❌ No se encontraron registros")
            
        conn.close()
        return None
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def analizar_en_oracle(archivo_oracle, documento):
    """Analiza el resultado en Oracle"""
    try:
        df = pd.read_csv(archivo_oracle, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df.columns = columnas[:len(df.columns)]
        
        reg = df[df['DOCUMENTO'].astype(str) == documento]
        
        if len(reg) > 0:
            registro = reg.iloc[0]
            print(f"   📊 Encontrado en Oracle: 1 registro")
            print(f"   📋 Registro Oracle:")
            print(f"      • {registro['OPERACION']} - {registro['FECHA_HORA']} - TID: {registro['TRANSACTIONID']}")
            print(f"        Celular: {registro['CELULAR']} - Empresa: {registro['EMPRESA']}")
            
            return {
                'timestamp': str(registro['FECHA_HORA']),
                'operacion': registro['OPERACION'],
                'transactionid': registro['TRANSACTIONID'],
                'celular': registro['CELULAR'],
                'empresa': registro['EMPRESA']
            }
        else:
            print(f"   ❌ No encontrado en Oracle")
            return None
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def analizar_en_modernizado(archivo_modernizado, documento):
    """Analiza el resultado en archivo modernizado"""
    try:
        df = pd.read_csv(archivo_modernizado, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df.columns = columnas[:len(df.columns)]
        
        reg = df[df['DOCUMENTO'].astype(str) == documento]
        
        if len(reg) > 0:
            registro = reg.iloc[0]
            print(f"   📊 Encontrado en Modernizado: 1 registro")
            print(f"   📋 Registro Modernizado:")
            print(f"      • {registro['OPERACION']} - {registro['FECHA_HORA']} - TID: {registro['TRANSACTIONID']}")
            print(f"        Celular: {registro['CELULAR']} - Empresa: {registro['EMPRESA']}")
            
            return {
                'timestamp': str(registro['FECHA_HORA']),
                'operacion': registro['OPERACION'],
                'transactionid': registro['TRANSACTIONID'],
                'celular': registro['CELULAR'],
                'empresa': registro['EMPRESA']
            }
        else:
            print(f"   ❌ No encontrado en Modernizado")
            return None
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def comparar_resultados(documento, resultado_asc, resultado_desc, resultado_oracle, resultado_modernizado):
    """Compara todos los resultados y determina la lógica correcta"""
    
    print(f"   🔍 ANÁLISIS COMPARATIVO DOCUMENTO {documento}:")
    
    if not resultado_oracle:
        print(f"   ❌ No se puede comparar - falta Oracle")
        return
    
    oracle_timestamp = resultado_oracle['timestamp']
    print(f"   📊 Oracle timestamp: {oracle_timestamp}")
    
    if resultado_asc:
        asc_timestamp = resultado_asc['timestamp']
        print(f"   📊 ASC timestamp: {asc_timestamp}")
        asc_match = oracle_timestamp == asc_timestamp
        print(f"   {'✅' if asc_match else '❌'} ASC coincide con Oracle: {asc_match}")
    
    if resultado_desc:
        desc_timestamp = resultado_desc['timestamp']
        print(f"   📊 DESC timestamp: {desc_timestamp}")
        desc_match = oracle_timestamp == desc_timestamp
        print(f"   {'✅' if desc_match else '❌'} DESC coincide con Oracle: {desc_match}")
    
    if resultado_modernizado:
        mod_timestamp = resultado_modernizado['timestamp']
        print(f"   📊 Modernizado timestamp: {mod_timestamp}")
        mod_match = oracle_timestamp == mod_timestamp
        print(f"   {'✅' if mod_match else '❌'} Modernizado coincide con Oracle: {mod_match}")
        
        if mod_match:
            print(f"   🏆 DOCUMENTO {documento}: HOMOLOGACIÓN PERFECTA")
        else:
            print(f"   ⚠️  DOCUMENTO {documento}: REQUIERE CORRECCIÓN")
            
            # Determinar qué lógica usar
            if resultado_asc and oracle_timestamp == resultado_asc['timestamp']:
                print(f"   💡 SOLUCIÓN: Usar deduplicación ASC para este documento")
            elif resultado_desc and oracle_timestamp == resultado_desc['timestamp']:
                print(f"   💡 SOLUCIÓN: Usar deduplicación DESC para este documento")
            else:
                print(f"   ⚠️  PROBLEMA: Ni ASC ni DESC coinciden - revisar lógica Oracle")

if __name__ == "__main__":
    print("🕵️ ANÁLISIS DETECTIVE - VERIFICACIÓN DE HOMOLOGACIÓN EXACTA")
    print("=" * 80)
    
    analisis_detective_completo()
    
    print(f"\n📋 CONCLUSIÓN FINAL:")
    print(f"   Este análisis determina si ASC/DESC es correcto o si hay")
    print(f"   algo más del proceso original que no estamos aplicando bien")
    
    print(f"\n✅ Análisis completado")
