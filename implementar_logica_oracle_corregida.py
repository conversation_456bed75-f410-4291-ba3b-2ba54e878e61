#!/usr/bin/env python3
"""
🥷 IMPLEMENTACIÓN DE LÓGICA ORACLE CORREGIDA
Lógica real: Prioridad MAÑANA > MADRUGADA > TARDE > NOCHE, luego MÁS ANTIGUO dentro del período
"""
import duckdb
import os

def implementar_logica_oracle_corregida():
    """Implementa la lógica Oracle corregida"""
    print("🥷 IMPLEMENTANDO LÓGICA ORACLE CORREGIDA")
    print("=" * 50)
    
    # Archivos
    log_usr_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    output_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_ORACLE_CORREGIDA.parquet"
    
    print(f"📄 Input: {log_usr_path}")
    print(f"📄 Output: {output_path}")
    
    print(f"\n🔧 LÓGICA ORACLE CORREGIDA:")
    print(f"   1. PRIORIDAD 1: MAÑANA (6:00-11:59)")
    print(f"   2. PRIORIDAD 2: MADRUGADA (0:00-5:59)")
    print(f"   3. PRIORIDAD 3: TARDE (12:00-17:59)")
    print(f"   4. PRIORIDAD 4: NOCHE (18:00-23:59)")
    print(f"   5. DENTRO DEL PERÍODO: MÁS ANTIGUO (CREATEDON ASC)")
    
    try:
        conn = duckdb.connect()
        
        # Query con lógica Oracle corregida
        oracle_corregida_query = f"""
        CREATE OR REPLACE TABLE log_usr_oracle_corregida AS
        WITH registros_con_prioridad AS (
            SELECT *,
                CASE 
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 1  -- MAÑANA
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 2   -- MADRUGADA
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 3 -- TARDE
                    ELSE 4  -- NOCHE (18-23)
                END as periodo_prioridad,
                CASE 
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 'MAÑANA'
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 'MADRUGADA'
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 'TARDE'
                    ELSE 'NOCHE'
                END as periodo_nombre,
                ROW_NUMBER() OVER (
                    PARTITION BY USERHISTID, REQUESTTYPE
                    ORDER BY 
                        CASE 
                            WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 1
                            WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 2
                            WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 3
                            ELSE 4
                        END ASC,  -- Prioridad más alta primero
                        CREATEDON ASC  -- MÁS ANTIGUO dentro del período elegido (CORREGIDO)
                ) as rn_final
            FROM read_parquet('{log_usr_path}')
        )
        SELECT 
            USERHISTID, CREATEDON, REQUESTTYPE, MSISDN, BANKDOMAIN, DOCUMENTO,
            TIPODOCUMENTO, NOMBRE, APELLIDO, NNOMBRE, NAPELLIDO, PERFILA, PERFILB,
            IDIOMAA, IDIOMAB, TELCOA, TELCOB, RAZON, PERFILCUENTA, PERFILCUENTAA,
            PERFILCUENTAB, TIPODOCUMENTOA, TIPODOCUMENTOB, DOCUMENTOB, NUMDOCUMENTOB,
            CREATED_BY, USERID, ACCOUNTTYPE, ACCOUNTID, MSISDNB,
            periodo_prioridad, periodo_nombre
        FROM registros_con_prioridad
        WHERE rn_final = 1  -- Solo el registro con mayor prioridad y más antiguo
        ORDER BY CREATEDON
        """
        
        print(f"\n⚙️  Ejecutando query Oracle corregida...")
        conn.execute(oracle_corregida_query)
        
        # Verificar resultados
        original_count = conn.execute(f"SELECT COUNT(*) FROM read_parquet('{log_usr_path}')").fetchone()[0]
        oracle_count = conn.execute("SELECT COUNT(*) FROM log_usr_oracle_corregida").fetchone()[0]
        
        print(f"📊 Registros originales: {original_count:,}")
        print(f"📊 Registros con lógica Oracle corregida: {oracle_count:,}")
        print(f"📊 Reducción: {original_count - oracle_count:,} registros")
        
        # Verificar documentos específicos
        print(f"\n🔍 VERIFICACIÓN DOCUMENTOS ESPECÍFICOS:")
        verificar_documentos_corregidos(conn)
        
        # Exportar resultado
        export_query = f"""
        COPY log_usr_oracle_corregida TO '{output_path}' (FORMAT PARQUET)
        """
        conn.execute(export_query)
        
        print(f"\n✅ Archivo generado: {output_path}")
        
        conn.close()
        return output_path
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def verificar_documentos_corregidos(conn):
    """Verifica que los documentos específicos tengan los timestamps correctos"""
    
    documentos_test = {
        "70635331": "2025-06-09 00:28:11",  # MADRUGADA vs NOCHE → MADRUGADA
        "76730654": "2025-06-09 09:41:55",  # MAÑANA vs MADRUGADA → MAÑANA  
        "60918019": "2025-06-09 07:31:58"   # MAÑANA vs MAÑANA → MÁS ANTIGUO
    }
    
    for documento, timestamp_esperado in documentos_test.items():
        query = f"""
        SELECT CREATEDON, periodo_nombre, periodo_prioridad
        FROM log_usr_oracle_corregida
        WHERE DOCUMENTO = '{documento}'
        """
        
        result = conn.execute(query).fetchall()
        
        if len(result) > 0:
            timestamp_obtenido, periodo, prioridad = result[0]
            timestamp_str = str(timestamp_obtenido)
            
            print(f"   📊 Doc {documento}:")
            print(f"      Esperado: {timestamp_esperado}")
            print(f"      Obtenido: {timestamp_str}")
            print(f"      Período: {periodo} (Prioridad {prioridad})")
            
            if timestamp_str == timestamp_esperado:
                print(f"      ✅ PERFECTO - Lógica Oracle corregida funciona")
            else:
                print(f"      ❌ ERROR - Lógica necesita más ajuste")
                print(f"         Diferencia: {timestamp_str} != {timestamp_esperado}")
        else:
            print(f"   ❌ Doc {documento}: No encontrado")

def generar_csv_con_logica_corregida():
    """Genera CSV final con la lógica Oracle corregida"""
    print(f"\n🔄 GENERANDO CSV CON LÓGICA ORACLE CORREGIDA...")
    
    # Usar el archivo con lógica Oracle corregida
    oracle_file = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_ORACLE_CORREGIDA.parquet"
    
    if not os.path.exists(oracle_file):
        print(f"❌ Archivo Oracle corregido no encontrado: {oracle_file}")
        return
    
    try:
        from S3_LOG_USER.procesar_log_usuarios import ProcesadorLogUsuarios
        import logging
        
        # Configurar logger
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        procesador = ProcesadorLogUsuarios(logger)
        csv_final_dir = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado"
        
        # Procesar con lógica Oracle corregida
        archivos_procesados = procesador.procesar_log_usuarios(
            oracle_file, "2025-06-09", csv_final_dir
        )
        
        print(f"✅ Archivos CSV generados con lógica Oracle corregida:")
        for archivo in archivos_procesados:
            print(f"   📁 {os.path.basename(archivo)}")
        
        return archivos_procesados
        
    except Exception as e:
        print(f"❌ Error generando CSV: {e}")
        return []

def verificar_homologacion_final():
    """Verifica la homologación final de los 3 documentos críticos"""
    print(f"\n🔍 VERIFICACIÓN HOMOLOGACIÓN FINAL:")
    
    # Buscar el archivo CSV más reciente
    csv_dir = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado"
    
    try:
        import glob
        archivos_csv = glob.glob(f"{csv_dir}/LOGUSR-FCOMPARTAMOS-*.csv")
        if archivos_csv:
            archivo_mas_reciente = max(archivos_csv, key=os.path.getctime)
            print(f"   📄 Archivo más reciente: {os.path.basename(archivo_mas_reciente)}")
            
            # Verificar los 3 documentos críticos
            documentos_criticos = ["70635331", "76730654", "60918019"]
            oracle_file = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
            
            for documento in documentos_criticos:
                verificar_documento_final(archivo_mas_reciente, oracle_file, documento)
        else:
            print(f"   ❌ No se encontraron archivos CSV")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def verificar_documento_final(archivo_modernizado, archivo_oracle, documento):
    """Verifica un documento específico en la homologación final"""
    try:
        import pandas as pd
        
        # Leer archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        # Buscar documento
        reg_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == documento]
        reg_oracle = df_oracle[df_oracle['DOCUMENTO'].astype(str) == documento]
        
        if len(reg_mod) > 0 and len(reg_oracle) > 0:
            mod_ts = reg_mod.iloc[0]['FECHA_HORA']
            oracle_ts = reg_oracle.iloc[0]['FECHA_HORA']
            
            if mod_ts == oracle_ts:
                print(f"   ✅ Doc {documento}: HOMOLOGACIÓN PERFECTA ({mod_ts})")
            else:
                print(f"   ❌ Doc {documento}: DIFERENCIA")
                print(f"      Modernizado: {mod_ts}")
                print(f"      Oracle: {oracle_ts}")
        else:
            print(f"   ❌ Doc {documento}: No encontrado en uno de los archivos")
            
    except Exception as e:
        print(f"   ❌ Error verificando doc {documento}: {e}")

if __name__ == "__main__":
    print("🥷 IMPLEMENTACIÓN DE LÓGICA ORACLE CORREGIDA")
    print("=" * 80)
    
    # Implementar lógica Oracle corregida
    archivo_oracle = implementar_logica_oracle_corregida()
    
    if archivo_oracle:
        print(f"\n🎯 LÓGICA ORACLE CORREGIDA IMPLEMENTADA")
        print(f"📄 Archivo: {archivo_oracle}")
        
        # Generar CSV final
        archivos_csv = generar_csv_con_logica_corregida()
        
        if archivos_csv:
            print(f"\n🏆 LÓGICA ORACLE CORREGIDA COMPLETADA")
            print(f"✅ Lógica Oracle corregida implementada")
            print(f"✅ CSV generados con homologación corregida")
            
            # Verificar homologación final
            verificar_homologacion_final()
            
            print(f"\n🎯 Resultado: Todos los documentos deben homologar perfectamente")
    
    print(f"\n✅ Implementación corregida completada")
