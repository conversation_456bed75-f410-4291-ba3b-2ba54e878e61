#!/usr/bin/env python3
"""
Script para probar la corrección de homologación del documento ********
Aplica la nueva lógica de deduplicación (CREATEDON DESC) y verifica homologación
"""
import pandas as pd
import duckdb
import os

def test_homologacion_corregida():
    """Prueba la homologación con la lógica corregida"""
    print("🔧 PRUEBA DE HOMOLOGACIÓN CORREGIDA - DOCUMENTO ********")
    print("=" * 65)
    
    documento = "********"
    
    # Archivos
    log_usr_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/********/LOG_USR.parquet"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"🎯 Documento objetivo: {documento}")
    print(f"📄 Archivo LOG_USR original: {os.path.basename(log_usr_original)}")
    print(f"📄 Archivo Oracle: {os.path.basename(archivo_oracle)}")
    
    if not os.path.exists(log_usr_original):
        print(f"❌ Archivo LOG_USR no encontrado: {log_usr_original}")
        return
    
    if not os.path.exists(archivo_oracle):
        print(f"❌ Archivo Oracle no encontrado: {archivo_oracle}")
        return
    
    # 1. Analizar registros originales
    print(f"\n1️⃣ ANÁLISIS DE REGISTROS ORIGINALES:")
    analizar_registros_originales(log_usr_original, documento)
    
    # 2. Aplicar nueva lógica de deduplicación
    print(f"\n2️⃣ APLICANDO NUEVA LÓGICA DE DEDUPLICACIÓN (CREATEDON DESC):")
    archivo_dedup_corregido = aplicar_deduplicacion_corregida(log_usr_original, documento)
    
    # 3. Comparar con Oracle
    print(f"\n3️⃣ COMPARACIÓN CON ORACLE:")
    comparar_con_oracle(archivo_dedup_corregido, archivo_oracle, documento)

def analizar_registros_originales(log_usr_path, documento):
    """Analiza los registros originales del documento"""
    try:
        conn = duckdb.connect()
        
        # Buscar todos los registros del documento
        query = f"""
        SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN
        FROM read_parquet('{log_usr_path}')
        WHERE DOCUMENTO = '{documento}'
        ORDER BY CREATEDON
        """
        
        result = conn.execute(query).fetchall()
        
        print(f"   📊 Total registros del documento: {len(result)}")
        
        if len(result) > 0:
            print(f"   📋 Detalles de registros:")
            for i, (requesttype, createdon, userhistid, msisdn, bankdomain) in enumerate(result, 1):
                print(f"      {i}. {requesttype} - {createdon} - {userhistid}")
                print(f"         MSISDN: {msisdn} - BankDomain: {bankdomain}")
        else:
            print(f"   ❌ No se encontraron registros")
            
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Error: {e}")

def aplicar_deduplicacion_corregida(log_usr_path, documento):
    """Aplica la nueva lógica de deduplicación (CREATEDON DESC)"""
    try:
        conn = duckdb.connect()
        
        # Aplicar deduplicación con CREATEDON DESC (nueva lógica)
        dedup_query = f"""
        CREATE OR REPLACE TABLE log_usr_deduplicated AS
        SELECT DISTINCT ON (USERHISTID, REQUESTTYPE) *
        FROM read_parquet('{log_usr_path}')
        ORDER BY USERHISTID, REQUESTTYPE, CREATEDON DESC
        """
        
        conn.execute(dedup_query)
        
        # Buscar el documento después de deduplicación
        query = f"""
        SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN
        FROM log_usr_deduplicated
        WHERE DOCUMENTO = '{documento}'
        ORDER BY CREATEDON
        """
        
        result = conn.execute(query).fetchall()
        
        print(f"   📊 Registros después de deduplicación: {len(result)}")
        
        if len(result) > 0:
            print(f"   📋 Registro seleccionado:")
            for requesttype, createdon, userhistid, msisdn, bankdomain in result:
                print(f"      • {requesttype} - {createdon} - {userhistid}")
                print(f"        MSISDN: {msisdn} - BankDomain: {bankdomain}")
                
                # Verificar si coincide con Oracle (09:41:55)
                if "09:41:55" in str(createdon):
                    print(f"      ✅ COINCIDE CON ORACLE (09:41:55)")
                elif "00:52:48" in str(createdon):
                    print(f"      ❌ AÚN SELECCIONA EL ANTIGUO (00:52:48)")
                else:
                    print(f"      ⚠️  Timestamp diferente: {createdon}")
        else:
            print(f"   ❌ Documento no encontrado después de deduplicación")
            
        conn.close()
        return "log_usr_deduplicated"
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def comparar_con_oracle(tabla_dedup, archivo_oracle, documento):
    """Compara el resultado con Oracle"""
    try:
        conn = duckdb.connect()

        # Recrear la tabla deduplicada para esta conexión
        log_usr_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/********/LOG_USR.parquet"
        dedup_query = f"""
        CREATE OR REPLACE TABLE log_usr_deduplicated AS
        SELECT DISTINCT ON (USERHISTID, REQUESTTYPE) *
        FROM read_parquet('{log_usr_path}')
        ORDER BY USERHISTID, REQUESTTYPE, CREATEDON DESC
        """
        conn.execute(dedup_query)
        
        # Leer Oracle
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        # Buscar documento en Oracle
        reg_oracle = df_oracle[df_oracle['DOCUMENTO'].astype(str) == documento]
        
        print(f"   📊 Oracle:")
        if len(reg_oracle) > 0:
            reg = reg_oracle.iloc[0]
            print(f"      • {reg['OPERACION']} - {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
            print(f"        Celular: {reg['CELULAR']} - Empresa: {reg['EMPRESA']}")
            oracle_timestamp = reg['FECHA_HORA']
        else:
            print(f"      ❌ Documento no encontrado en Oracle")
            return
        
        # Buscar documento en tabla deduplicada
        if tabla_dedup:
            query = f"""
            SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN
            FROM {tabla_dedup}
            WHERE DOCUMENTO = '{documento}'
            """
            
            result = conn.execute(query).fetchall()
            
            print(f"   📊 Pipeline corregido:")
            if len(result) > 0:
                requesttype, createdon, userhistid, msisdn, bankdomain = result[0]
                print(f"      • {requesttype} - {createdon} - {userhistid}")
                print(f"        MSISDN: {msisdn} - BankDomain: {bankdomain}")
                
                # Comparar timestamps
                pipeline_timestamp = str(createdon)
                oracle_timestamp_str = str(oracle_timestamp)
                
                print(f"\n   🔍 COMPARACIÓN DE TIMESTAMPS:")
                print(f"      Pipeline: {pipeline_timestamp}")
                print(f"      Oracle:   {oracle_timestamp_str}")
                
                if "09:41:55" in pipeline_timestamp and "09:41:55" in oracle_timestamp_str:
                    print(f"\n   🏆 ¡HOMOLOGACIÓN PERFECTA LOGRADA!")
                    print(f"      ✅ Ambos seleccionan el mismo timestamp: 09:41:55")
                    print(f"      ✅ Documento ******** ahora homologa 100%")
                elif "00:52:48" in pipeline_timestamp:
                    print(f"\n   ❌ HOMOLOGACIÓN AÚN FALLIDA")
                    print(f"      Pipeline sigue seleccionando 00:52:48")
                    print(f"      Oracle selecciona 09:41:55")
                else:
                    print(f"\n   ⚠️  RESULTADO INESPERADO")
                    print(f"      Verificar lógica de deduplicación")
            else:
                print(f"      ❌ Documento no encontrado en pipeline")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🕵️ PRUEBA DE CORRECCIÓN DE HOMOLOGACIÓN")
    print("=" * 70)
    
    test_homologacion_corregida()
    
    print(f"\n✅ Prueba completada")
