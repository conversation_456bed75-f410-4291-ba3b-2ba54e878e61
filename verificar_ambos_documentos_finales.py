#!/usr/bin/env python3
"""
Verificación final de ambos documentos críticos con la lógica Oracle ninja implementada
Documentos: 70635331 y 76730654
Objetivo: Confirmar que AMBOS cuadran perfectamente "como dos gotas de agua"
"""
import pandas as pd

def verificar_ambos_documentos():
    """Verifica que ambos documentos cuadren perfectamente"""
    print("🔍 VERIFICACIÓN FINAL - AMBOS DOCUMENTOS CRÍTICOS")
    print("=" * 60)
    
    # Documentos a verificar
    documentos = ["70635331", "76730654"]
    
    # Archivos finales
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609210505.csv"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"📄 Modernizado (NINJA): {archivo_modernizado.split('/')[-1]}")
    print(f"📄 Oracle: {archivo_oracle.split('/')[-1]}")
    
    # Resultados esperados según la lógica ninja
    resultados_esperados = {
        "70635331": {
            "timestamp": "2025-06-09 00:28:11",
            "periodo": "MADRUGADA",
            "prioridad": 2
        },
        "76730654": {
            "timestamp": "2025-06-09 09:41:55", 
            "periodo": "MAÑANA",
            "prioridad": 1
        }
    }
    
    print(f"\n🎯 RESULTADOS ESPERADOS (LÓGICA NINJA):")
    for doc, esperado in resultados_esperados.items():
        print(f"   Doc {doc}: {esperado['timestamp']} ({esperado['periodo']} - Prioridad {esperado['prioridad']})")
    
    # Verificar cada documento
    resultados_verificacion = {}
    
    for documento in documentos:
        print(f"\n{'='*50}")
        print(f"🔍 VERIFICANDO DOCUMENTO: {documento}")
        print(f"{'='*50}")
        
        resultado = verificar_documento_individual(
            documento, 
            archivo_modernizado, 
            archivo_oracle,
            resultados_esperados[documento]
        )
        
        resultados_verificacion[documento] = resultado
    
    # Resumen final
    print(f"\n{'='*60}")
    print(f"📋 RESUMEN FINAL - VERIFICACIÓN COMPLETA")
    print(f"{'='*60}")
    
    documentos_perfectos = 0
    
    for documento, resultado in resultados_verificacion.items():
        if resultado['homologacion_perfecta']:
            print(f"✅ Documento {documento}: HOMOLOGACIÓN PERFECTA")
            documentos_perfectos += 1
        else:
            print(f"❌ Documento {documento}: REQUIERE CORRECCIÓN")
            print(f"   Problema: {resultado['problema']}")
    
    print(f"\n🏆 RESULTADO FINAL:")
    if documentos_perfectos == len(documentos):
        print(f"   ✅ AMBOS DOCUMENTOS CUADRAN PERFECTAMENTE")
        print(f"   🎯 HOMOLOGACIÓN 'COMO DOS GOTAS DE AGUA' LOGRADA")
        print(f"   🥷 LÓGICA NINJA ORACLE FUNCIONA AL 100%")
    else:
        print(f"   ⚠️  {documentos_perfectos}/{len(documentos)} documentos homologados")
        print(f"   🔧 REQUIERE AJUSTES ADICIONALES")
    
    return documentos_perfectos == len(documentos)

def verificar_documento_individual(documento, archivo_modernizado, archivo_oracle, esperado):
    """Verifica un documento individual"""
    
    resultado = {
        'homologacion_perfecta': False,
        'problema': None,
        'modernizado': None,
        'oracle': None
    }
    
    try:
        # Leer archivo modernizado
        print(f"   📊 MODERNIZADO:")
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df_mod.columns = columnas[:len(df_mod.columns)]
        
        reg_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == documento]
        
        if len(reg_mod) > 0:
            mod_reg = reg_mod.iloc[0]
            mod_timestamp = mod_reg['FECHA_HORA']
            print(f"      Encontrado: {mod_reg['OPERACION']} - {mod_timestamp}")
            print(f"      TID: {mod_reg['TRANSACTIONID']}")
            print(f"      Celular: {mod_reg['CELULAR']} - Empresa: {mod_reg['EMPRESA']}")
            
            resultado['modernizado'] = {
                'timestamp': mod_timestamp,
                'operacion': mod_reg['OPERACION'],
                'transactionid': mod_reg['TRANSACTIONID'],
                'celular': mod_reg['CELULAR'],
                'empresa': mod_reg['EMPRESA']
            }
        else:
            print(f"      ❌ NO ENCONTRADO")
            resultado['problema'] = "No encontrado en modernizado"
            return resultado
        
        # Leer archivo Oracle
        print(f"   📊 ORACLE:")
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        reg_oracle = df_oracle[df_oracle['DOCUMENTO'].astype(str) == documento]
        
        if len(reg_oracle) > 0:
            oracle_reg = reg_oracle.iloc[0]
            oracle_timestamp = oracle_reg['FECHA_HORA']
            print(f"      Encontrado: {oracle_reg['OPERACION']} - {oracle_timestamp}")
            print(f"      TID: {oracle_reg['TRANSACTIONID']}")
            print(f"      Celular: {oracle_reg['CELULAR']} - Empresa: {oracle_reg['EMPRESA']}")
            
            resultado['oracle'] = {
                'timestamp': oracle_timestamp,
                'operacion': oracle_reg['OPERACION'],
                'transactionid': oracle_reg['TRANSACTIONID'],
                'celular': oracle_reg['CELULAR'],
                'empresa': oracle_reg['EMPRESA']
            }
        else:
            print(f"      ❌ NO ENCONTRADO")
            resultado['problema'] = "No encontrado en Oracle"
            return resultado
        
        # Comparación detallada
        print(f"   🔍 COMPARACIÓN DETALLADA:")
        
        # Verificar timestamp
        if mod_timestamp == oracle_timestamp:
            print(f"      ✅ TIMESTAMP: {mod_timestamp} (PERFECTO)")
            timestamp_ok = True
        else:
            print(f"      ❌ TIMESTAMP:")
            print(f"         Modernizado: {mod_timestamp}")
            print(f"         Oracle:      {oracle_timestamp}")
            timestamp_ok = False
        
        # Verificar timestamp esperado
        if mod_timestamp == esperado['timestamp']:
            print(f"      ✅ TIMESTAMP NINJA: Coincide con esperado ({esperado['periodo']})")
            ninja_ok = True
        else:
            print(f"      ❌ TIMESTAMP NINJA:")
            print(f"         Obtenido: {mod_timestamp}")
            print(f"         Esperado: {esperado['timestamp']} ({esperado['periodo']})")
            ninja_ok = False
        
        # Verificar otros campos
        operacion_ok = resultado['modernizado']['operacion'] == resultado['oracle']['operacion']
        celular_ok = resultado['modernizado']['celular'] == resultado['oracle']['celular']
        empresa_ok = resultado['modernizado']['empresa'] == resultado['oracle']['empresa']
        
        print(f"      {'✅' if operacion_ok else '❌'} OPERACIÓN: {resultado['modernizado']['operacion']} vs {resultado['oracle']['operacion']}")
        print(f"      {'✅' if celular_ok else '❌'} CELULAR: {resultado['modernizado']['celular']} vs {resultado['oracle']['celular']}")
        print(f"      {'✅' if empresa_ok else '❌'} EMPRESA: {resultado['modernizado']['empresa']} vs {resultado['oracle']['empresa']}")
        
        # TransactionID (puede ser diferente)
        tid_diferente = resultado['modernizado']['transactionid'] != resultado['oracle']['transactionid']
        print(f"      {'✅' if tid_diferente else '⚠️'} TRANSACTIONID: {'Diferente (OK)' if tid_diferente else 'Igual (inesperado)'}")
        
        # Resultado final
        if timestamp_ok and ninja_ok and operacion_ok and celular_ok and empresa_ok:
            print(f"      🏆 HOMOLOGACIÓN PERFECTA - 'COMO DOS GOTAS DE AGUA'")
            resultado['homologacion_perfecta'] = True
        else:
            problemas = []
            if not timestamp_ok: problemas.append("timestamp diferente")
            if not ninja_ok: problemas.append("timestamp ninja incorrecto")
            if not operacion_ok: problemas.append("operación diferente")
            if not celular_ok: problemas.append("celular diferente")
            if not empresa_ok: problemas.append("empresa diferente")
            
            resultado['problema'] = ", ".join(problemas)
            print(f"      ❌ PROBLEMAS: {resultado['problema']}")
        
        return resultado
        
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        resultado['problema'] = f"Error: {e}"
        return resultado

if __name__ == "__main__":
    print("🔍 VERIFICACIÓN FINAL - DOCUMENTOS CRÍTICOS")
    print("=" * 80)
    
    exito = verificar_ambos_documentos()
    
    if exito:
        print(f"\n🎉 ¡MISIÓN COMPLETADA CON ÉXITO!")
        print(f"🏆 AMBOS DOCUMENTOS HOMOLOGADOS PERFECTAMENTE")
    else:
        print(f"\n⚠️  MISIÓN REQUIERE AJUSTES ADICIONALES")
    
    print(f"\n✅ Verificación completada")
